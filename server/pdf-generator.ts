import puppeteer from "puppeteer";
import { FullReportData } from "@shared/schema";
import { generateReportHTML } from "./html-generator";
import { objectStorage } from "./object-storage";
import fs from "fs";
import path from "path";
import { nanoid } from "nanoid";

// Fallback PDF generation
import * as htmlPdf from 'html-pdf-node';

/**
 * Generate a PDF from report data and save it with object storage fallback
 */
export async function generateAndSavePDF(
  fullReport: FullReportData,
  analysisId: number,
  projectName: string = "Unknown Project"
): Promise<string | null> {
  try {
    console.log(`Starting PDF generation for analysis #${analysisId}`);

    // Generate the PDF buffer
    const pdfBuffer = await generatePDFBuffer(fullReport, analysisId, projectName);
    if (!pdfBuffer) {
      return null;
    }

    // Try to save to object storage first
    return await savePDFWithFallback(pdfBuffer, analysisId);

  } catch (error) {
    console.error(`Error in generateAndSavePDF for analysis #${analysisId}:`, error);
    return null;
  }
}

/**
 * Save PDF to object storage with local disk fallback
 */
export async function savePDFWithFallback(pdfBuffer: Buffer, analysisId: number): Promise<string | null> {
  try {
    // Try object storage first
    const objectKey = await objectStorage.uploadPDF(pdfBuffer, analysisId);
    console.log(`PDF uploaded to object storage: ${objectKey}`);
    return `/api/report/download-from-storage/${analysisId}/${encodeURIComponent(objectKey)}`;
  } catch (objectStorageError) {
    console.error('Object storage upload failed, falling back to local disk:', objectStorageError);

    // Fallback to local disk storage
    try {
      const localUrl = await savePDFToDisk(pdfBuffer, analysisId);
      console.log(`PDF saved to local disk as fallback: ${localUrl}`);
      return localUrl;
    } catch (diskError) {
      console.error('Local disk fallback also failed:', diskError);
      return null;
    }
  }
}

/**
 * Generate PDF buffer using Puppeteer with retry mechanism
 */
export async function generatePDFBuffer(
  fullReport: FullReportData,
  analysisId: number,
  projectName: string = "Unknown Project"
): Promise<Buffer | null> {
  // Detect if running in Replit environment
  const isReplit = process.env.REPL_ID || process.env.REPLIT_DB_URL || process.env.REPL_SLUG;

  // In Replit, try fallback first if we've had recent Puppeteer failures
  if (isReplit && process.env.PUPPETEER_FALLBACK_MODE === 'true') {
    console.log(`Replit environment detected with fallback mode enabled, using html-pdf-node for analysis #${analysisId}`);
    try {
      return await generatePDFBufferFallback(fullReport, analysisId, projectName);
    } catch (fallbackError) {
      console.error('Fallback PDF generation failed, trying Puppeteer...', fallbackError);
    }
  }

  const maxRetries = isReplit ? 2 : 3; // Fewer retries in Replit
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(`PDF generation attempt ${attempt}/${maxRetries} for analysis #${analysisId}`);

    try {
      return await generatePDFBufferInternal(fullReport, analysisId, projectName);
    } catch (error) {
      lastError = error as Error;
      console.error(`PDF generation attempt ${attempt} failed:`, error);

      // In Replit, if we get a protocol timeout, immediately try fallback
      if (isReplit && error instanceof Error && error.message.includes('ProtocolError')) {
        console.log('Protocol error detected in Replit, switching to fallback mode');
        process.env.PUPPETEER_FALLBACK_MODE = 'true';
        break;
      }

      if (attempt < maxRetries) {
        const delay = attempt * (isReplit ? 3000 : 2000); // Longer delays in Replit
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  console.error(`All PDF generation attempts failed for analysis #${analysisId}. Last error:`, lastError);

  // Try fallback PDF generation
  try {
    console.log(`Attempting fallback PDF generation for analysis #${analysisId}`);
    return await generatePDFBufferFallback(fullReport, analysisId, projectName);
  } catch (fallbackError) {
    console.error('Fallback PDF generation also failed:', fallbackError);
    return null;
  }
}

/**
 * Internal PDF generation function (original logic)
 */
async function generatePDFBufferInternal(
  fullReport: FullReportData,
  analysisId: number,
  projectName: string = "Unknown Project"
): Promise<Buffer | null> {
  let browser;

  try {
    // Generate HTML content for the report
    const htmlContent = await generateReportHTML(fullReport, projectName);

    // Detect if running in Replit environment
    const isReplit = process.env.REPL_ID || process.env.REPLIT_DB_URL || process.env.REPL_SLUG;

    // Configure launch options based on environment
    const launchOptions = {
      headless: true,
      args: isReplit ? [
        // Replit-optimized Chrome args
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--single-process',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor,AudioServiceOutOfProcess',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-default-apps',
        '--disable-translate',
        '--disable-sync',
        '--hide-scrollbars',
        '--mute-audio',
        '--no-default-browser-check',
        '--no-first-run',
        '--disable-ipc-flooding-protection',
        '--disable-background-networking',
        '--metrics-recording-only',
        '--safebrowsing-disable-auto-update',
        '--disable-client-side-phishing-detection',
        '--disable-component-extensions-with-background-pages',
        '--disable-hang-monitor',
        '--disable-popup-blocking',
        '--disable-prompt-on-repost',
        '--force-color-profile=srgb',
        '--password-store=basic',
        '--use-mock-keychain',
        '--disable-accelerated-2d-canvas',
        '--no-zygote',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-features=TranslateUI,BlinkGenPropertyTrees,AudioServiceOutOfProcess,VizDisplayCompositor',
        '--disable-ipc-flooding-protection'
      ] : [
        // Standard Chrome args for other environments
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-default-apps',
        '--disable-translate',
        '--disable-sync',
        '--hide-scrollbars',
        '--mute-audio',
        '--no-default-browser-check',
        '--no-pings',
        '--disable-ipc-flooding-protection'
      ],
      timeout: isReplit ? 120000 : 60000, // Longer timeout for Replit
      protocolTimeout: isReplit ? 120000 : 60000, // Longer protocol timeout for Replit
      slowMo: isReplit ? 200 : 100, // Slower operations in Replit
      ...(isReplit && {
        // Additional Replit-specific options
        ignoreDefaultArgs: ['--disable-extensions'],
        dumpio: false, // Disable dumping IO to reduce noise
      })
    };

    if (isReplit) {
      console.log('Detected Replit environment, using optimized Chrome configuration');
    }

    // Try with custom executable path first
    if (process.env.PUPPETEER_EXECUTABLE_PATH) {
      try {
        browser = await puppeteer.launch({
          ...launchOptions,
          executablePath: process.env.PUPPETEER_EXECUTABLE_PATH
        });
      } catch (error) {
        console.log('Custom executable path failed, trying default...', error);
      }
    }

    // If custom path failed or not set, try default
    if (!browser) {
      try {
        browser = await puppeteer.launch(launchOptions);
      } catch (error) {
        console.error('Default browser launch failed, trying with minimal args...', error);

        // Try with minimal args for cloud environments
        const minimalOptions = {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--single-process',
            '--disable-background-networking',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-features=TranslateUI,BlinkGenPropertyTrees'
          ],
          timeout: 120000,
          protocolTimeout: 120000
        };

        browser = await puppeteer.launch(minimalOptions);
      }
    }
    
    // Create page with retry mechanism for CDP connection issues
    let page;
    const maxPageRetries = 3;
    for (let pageAttempt = 1; pageAttempt <= maxPageRetries; pageAttempt++) {
      try {
        console.log(`Creating new page, attempt ${pageAttempt}/${maxPageRetries}`);
        page = await browser.newPage();
        break;
      } catch (pageError) {
        console.error(`Page creation attempt ${pageAttempt} failed:`, pageError);
        if (pageAttempt === maxPageRetries) {
          throw pageError;
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, pageAttempt * 3000));
      }
    }

    if (!page) {
      throw new Error('Failed to create page after all retries');
    }

    // Set page timeout for cloud environments
    page.setDefaultTimeout(90000);
    page.setDefaultNavigationTimeout(90000);

    // Set the HTML content with more lenient wait conditions
    await page.setContent(htmlContent, {
      waitUntil: 'domcontentloaded', // Changed from 'networkidle0' to be more reliable
      timeout: 60000
    });
    
    // Generate PDF with proper formatting
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20mm',
        right: '15mm',
        bottom: '20mm',
        left: '15mm'
      },
      displayHeaderFooter: true,
      headerTemplate: `
        <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
          MVP Readiness Report - ${projectName}
        </div>
      `,
      footerTemplate: `
        <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
          Page <span class="pageNumber"></span> of <span class="totalPages"></span>
        </div>
      `
    });
    
    // Close the browser
    await browser.close();
    
    console.log(`PDF generation completed for analysis #${analysisId}`);
    return Buffer.from(pdfBuffer);

  } catch (error) {
    console.error(`Error generating PDF buffer for analysis #${analysisId}:`, error);

    // Log specific Chrome/Puppeteer errors
    const errorMessage = error instanceof Error ? error.message : String(error);

    if (errorMessage.includes('libnspr4.so') || errorMessage.includes('shared libraries')) {
      console.error('Chrome dependencies missing. Please install Chrome dependencies.');
      console.error('sudo apt-get install -y libnss3 libnspr4 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2');
    } else if (errorMessage.includes('ProtocolError') || errorMessage.includes('timed out')) {
      console.error('Protocol timeout error detected. This is common in cloud environments.');
      console.error('Consider increasing timeout values or using fallback PDF generation.');
    } else if (errorMessage.includes('Failed to launch') || errorMessage.includes('spawn')) {
      console.error('Browser launch failed. Check if Chrome/Chromium is installed and accessible.');
    }

    // The error will be caught by the retry mechanism in generatePDFBuffer
    throw error;
  } finally {
    // Ensure browser is closed
    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error('Error closing browser:', closeError);
      }
    }
  }
}

/**
 * Fallback PDF generation using html-pdf-node (doesn't require Chrome)
 */
async function generatePDFBufferFallback(
  fullReport: FullReportData,
  analysisId: number,
  projectName: string = "Unknown Project"
): Promise<Buffer | null> {
  try {
    // Generate HTML content for the report
    const htmlContent = await generateReportHTML(fullReport, projectName);

    // Configure html-pdf-node options
    const options = {
      format: 'A4',
      margin: {
        top: '20mm',
        right: '15mm',
        bottom: '20mm',
        left: '15mm'
      },
      printBackground: true,
      displayHeaderFooter: true,
      headerTemplate: `
        <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
          MVP Readiness Report - ${projectName}
        </div>
      `,
      footerTemplate: `
        <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
          Page <span class="pageNumber"></span> of <span class="totalPages"></span>
        </div>
      `
    };

    // Generate PDF using html-pdf-node
    const file = { content: htmlContent };
    const pdfBuffer = await new Promise<Buffer>((resolve, reject) => {
      htmlPdf.generatePdf(file, options, (err: any, buffer: Buffer) => {
        if (err) {
          reject(err);
        } else {
          resolve(buffer);
        }
      });
    });

    console.log(`Fallback PDF generated successfully for analysis #${analysisId}`);
    return pdfBuffer;

  } catch (error) {
    console.error(`Error in fallback PDF generation for analysis #${analysisId}:`, error);
    throw error;
  }
}

/**
 * Save PDF buffer to disk and return download URL
 */
export async function savePDFToDisk(pdfBuffer: Buffer, analysisId: number): Promise<string | null> {
  try {
    const reportsDir = path.join(process.cwd(), 'reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const fileName = `report-${analysisId}-${nanoid()}.pdf`;
    const filePath = path.join(reportsDir, fileName);
    fs.writeFileSync(filePath, pdfBuffer);
    
    const reportUrl = `/api/report/download/${analysisId}/${fileName}`;
    console.log(`PDF saved successfully: ${filePath}`);
    
    return reportUrl;
    
  } catch (error) {
    console.error("Error saving PDF to disk:", error);
    return null;
  }
}

/**
 * Check if PDF file exists
 */
export function pdfExists(filename: string): boolean {
  const filePath = path.join(process.cwd(), 'reports', filename);
  return fs.existsSync(filePath);
}

/**
 * Get PDF file path
 */
export function getPDFPath(filename: string): string {
  return path.join(process.cwd(), 'reports', filename);
}

# Replit Environment Variables for <PERSON><PERSON>peteer

Add these environment variables in your Replit deployment settings:

## Required Environment Variables

```bash
# Puppeteer Configuration
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# Chrome/Chromium path alternatives (try these if above doesn't work)
# PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
# PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome

# Memory and performance settings
NODE_OPTIONS=--max-old-space-size=2048

# Disable Chrome sandbox (already in code but good to have as env var)
CHROME_NO_SANDBOX=true
```

## Replit Deployment Configuration

In your `replit.nix` file, make sure you have Chrome installed:

```nix
{ pkgs }: {
  deps = [
    pkgs.nodejs-18_x
    pkgs.google-chrome
    # or alternatively:
    # pkgs.chromium
  ];
}
```

## Alternative: Use Puppeteer with bundled Chromium

If the above doesn't work, you can also try using the bundled Chromium:

```bash
# Remove the executable path override
unset PUPPETEER_EXECUTABLE_PATH
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=false
```

## Testing the Setup

You can test if Chrome is available by running:

```bash
which google-chrome-stable
which chromium-browser
which google-chrome
```

One of these should return a path if Chrome is properly installed.

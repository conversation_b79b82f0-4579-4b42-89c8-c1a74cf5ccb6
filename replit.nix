{ pkgs }: {
  deps = [
    pkgs.nodejs-18_x
    pkgs.nodePackages.npm
    pkgs.nodePackages.typescript

    # Chromium browser
    pkgs.chromium

    # Essential system libraries that Chromium needs but might not be linked properly in containers
    pkgs.nss
    pkgs.nspr
    pkgs.atk
    pkgs.at-spi2-atk
    pkgs.cups
    pkgs.gtk3
    pkgs.gdk-pixbuf
    pkgs.libxss
    pkgs.alsa-lib

    # X11 libraries (required even for headless mode)
    pkgs.xorg.libX11
    pkgs.xorg.libXcomposite
    pkgs.xorg.libXdamage
    pkgs.xorg.libXext
    pkgs.xorg.libXfixes
    pkgs.xorg.libXrandr
    pkgs.xorg.libXrender
    pkgs.xorg.libxcb

    # Font and rendering libraries
    pkgs.fontconfig
    pkgs.freetype
    pkgs.harfbuzz

    # Additional system libraries
    pkgs.glib
    pkgs.dbus
    pkgs.libuuid
  ];

  env = {
    # Puppeteer configuration for Chromium
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD = "true";
    PUPPETEER_EXECUTABLE_PATH = "${pkgs.chromium}/bin/chromium";

    # Chrome configuration
    CHROME_NO_SANDBOX = "true";
    DISPLAY = ":99";

    # Node.js configuration
    NODE_OPTIONS = "--max-old-space-size=2048";

    # Disable Chrome's crash reporting and other features that might cause issues
    GOOGLE_API_KEY = "no";
    GOOGLE_DEFAULT_CLIENT_ID = "no";
    GOOGLE_DEFAULT_CLIENT_SECRET = "no";
  };
}

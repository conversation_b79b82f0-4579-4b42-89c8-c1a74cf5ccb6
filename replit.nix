{ pkgs }: {
  deps = [
    pkgs.nodejs-18_x
    pkgs.nodePackages.npm
    pkgs.nodePackages.typescript
    
    # Chrome/Chromium with all required dependencies
    pkgs.google-chrome
    
    # Alternative if google-chrome doesn't work
    # pkgs.chromium
    
    # Required system libraries for Chrome
    pkgs.nss
    pkgs.nspr
    pkgs.atk
    pkgs.at-spi2-atk
    pkgs.cups
    pkgs.drm
    pkgs.gtk3
    pkgs.gdk-pixbuf
    pkgs.gsettings-desktop-schemas
    pkgs.libxss
    pkgs.libasound2
    pkgs.fontconfig
    pkgs.freetype
    pkgs.harfbuzz
    pkgs.icu
    pkgs.libpng
    pkgs.libjpeg
    pkgs.libwebp
    pkgs.libxslt
    pkgs.libxml2
    
    # X11 libraries (even for headless)
    pkgs.xorg.libX11
    pkgs.xorg.libXcomposite
    pkgs.xorg.libXcursor
    pkgs.xorg.libXdamage
    pkgs.xorg.libXext
    pkgs.xorg.libXfixes
    pkgs.xorg.libXi
    pkgs.xorg.libXrandr
    pkgs.xorg.libXrender
    pkgs.xorg.libXtst
    pkgs.xorg.libxcb
    
    # Additional dependencies
    pkgs.glib
    pkgs.dbus
    pkgs.systemd
    pkgs.libnotify
    pkgs.libuuid
    pkgs.xdg-utils
    pkgs.lsb-release
    pkgs.wget
    pkgs.gnused
    pkgs.gawk
    pkgs.coreutils
  ];
  
  env = {
    # Chrome/Puppeteer environment variables
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD = "true";
    PUPPETEER_EXECUTABLE_PATH = "${pkgs.google-chrome}/bin/google-chrome-stable";
    
    # Alternative paths to try if the above doesn't work
    # PUPPETEER_EXECUTABLE_PATH = "${pkgs.chromium}/bin/chromium";
    # PUPPETEER_EXECUTABLE_PATH = "/usr/bin/google-chrome-stable";
    
    # Chrome configuration
    CHROME_NO_SANDBOX = "true";
    DISPLAY = ":99";
    
    # Node.js configuration
    NODE_OPTIONS = "--max-old-space-size=2048";
    
    # Disable Chrome's crash reporting and other features that might cause issues
    CHROME_DEVEL_SANDBOX = "/usr/local/sbin/chrome-devel-sandbox";
    GOOGLE_API_KEY = "no";
    GOOGLE_DEFAULT_CLIENT_ID = "no";
    GOOGLE_DEFAULT_CLIENT_SECRET = "no";
  };
}
